package com.example.demo.service;

import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.protocol.HttpProcessor;
import org.apache.http.protocol.HttpProcessorBuilder;
import org.apache.http.protocol.RequestTargetHost;
import org.apache.http.protocol.RequestUserAgent;
import org.apache.http.ssl.SSLContextBuilder;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import java.io.FileInputStream;
import java.security.KeyStore;

@Service
public class HttpClientService {

    public CloseableHttpClient createHttpClientForEmpresa(String empresa) throws Exception {

        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        keyStore.load(new FileInputStream("C:\\certificados-chaves\\FEDERAL\\FEDERAL_GOIANIA.pfx"), "Federallcom13".toCharArray());

        SSLContext sslContext = SSLContextBuilder.create()
                .loadKeyMaterial(keyStore, "Federallcom13".toCharArray())
                .loadTrustMaterial(null, (chain, authType) -> true)
                .build();

        HttpProcessor httpProcessor = HttpProcessorBuilder.create()
                .add(new RequestTargetHost())
                .add(new RequestUserAgent("Spring WS"))
                .build();

        return HttpClients.custom()
                .setSSLSocketFactory(new SSLConnectionSocketFactory(sslContext))
                .setHttpProcessor(httpProcessor)
                .disableContentCompression()
                .build();
    }
}