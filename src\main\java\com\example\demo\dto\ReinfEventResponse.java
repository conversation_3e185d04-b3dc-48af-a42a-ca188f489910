package com.example.demo.dto;

import lombok.Data;

@Data
public class ReinfEventResponse {
    private boolean success;
    private String message;
    private String protocolNumber;
    private String receiptXml;
    private String errorDetails;
    
    public static ReinfEventResponse success(String protocolNumber, String receiptXml) {
        ReinfEventResponse response = new ReinfEventResponse();
        response.setSuccess(true);
        response.setMessage("Evento enviado com sucesso");
        response.setProtocolNumber(protocolNumber);
        response.setReceiptXml(receiptXml);
        return response;
    }
    
    public static ReinfEventResponse error(String message, String errorDetails) {
        ReinfEventResponse response = new ReinfEventResponse();
        response.setSuccess(false);
        response.setMessage(message);
        response.setErrorDetails(errorDetails);
        return response;
    }
}
