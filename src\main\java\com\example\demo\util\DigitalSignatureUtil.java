package com.example.demo.util;

import org.apache.xml.security.Init;
import org.apache.xml.security.c14n.Canonicalizer;
import org.apache.xml.security.signature.XMLSignature;
import org.apache.xml.security.transforms.Transforms;
import org.apache.xml.security.utils.Constants;
import org.apache.xml.security.utils.XMLUtils;
import org.apache.xml.security.keys.content.X509Data;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.ByteArrayInputStream;
import java.io.FileInputStream;
import java.io.StringWriter;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.Security;
import java.security.cert.X509Certificate;

@Component
public class DigitalSignatureUtil {

    static {
        if (!Init.isInitialized()) {
            Init.init();
        }
        Security.addProvider(new BouncyCastleProvider());
    }

    public String signXml(String xmlContent, String certificatePath, String certificatePassword) throws Exception {
        // Carregar certificado
        KeyStore keyStore = KeyStore.getInstance("PKCS12");
        try (FileInputStream fis = new FileInputStream(certificatePath)) {
            keyStore.load(fis, certificatePassword.toCharArray());
        }

        String alias = keyStore.aliases().nextElement();
        PrivateKey privateKey = (PrivateKey) keyStore.getKey(alias, certificatePassword.toCharArray());
        X509Certificate certificate = (X509Certificate) keyStore.getCertificate(alias);

        // Parse XML
        DocumentBuilderFactory dbf = DocumentBuilderFactory.newInstance();
        dbf.setNamespaceAware(true);
        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.parse(new ByteArrayInputStream(xmlContent.getBytes("UTF-8")));

        // Encontrar o elemento a ser assinado (elemento com atributo id)
        Element elementToSign = findElementWithId(doc.getDocumentElement());
        if (elementToSign == null) {
            throw new Exception("Elemento com ID não encontrado para assinatura");
        }

        String id = elementToSign.getAttribute("id");
        if (id == null || id.isEmpty()) {
            throw new Exception("Atributo ID não encontrado no elemento");
        }

        // Registrar o elemento com ID para resolução
        elementToSign.setIdAttribute("id", true);

        // Criar assinatura XML
        XMLSignature xmlSignature = new XMLSignature(doc, null, XMLSignature.ALGO_ID_SIGNATURE_RSA_SHA256);
        
        // Adicionar a assinatura como último elemento do documento
        doc.getDocumentElement().appendChild(xmlSignature.getElement());

        // Criar transforms
        Transforms transforms = new Transforms(doc);
        transforms.addTransform(Transforms.TRANSFORM_ENVELOPED_SIGNATURE);
        transforms.addTransform(Canonicalizer.ALGO_ID_C14N_OMIT_COMMENTS);

        // Adicionar referência
        xmlSignature.addDocument("#" + id, transforms, "http://www.w3.org/2001/04/xmlenc#sha256");

        // Assinar
        xmlSignature.sign(privateKey);

        // Adicionar certificado à assinatura
        X509Data x509Data = new X509Data(doc);
        x509Data.addCertificate(certificate);
        xmlSignature.getKeyInfo().add(x509Data);

        // Converter de volta para string
        TransformerFactory tf = TransformerFactory.newInstance();
        Transformer transformer = tf.newTransformer();
        StringWriter writer = new StringWriter();
        transformer.transform(new DOMSource(doc), new StreamResult(writer));

        return writer.getBuffer().toString();
    }

    private Element findElementWithId(Element element) {
        if (element.hasAttribute("id")) {
            return element;
        }
        
        for (int i = 0; i < element.getChildNodes().getLength(); i++) {
            if (element.getChildNodes().item(i) instanceof Element) {
                Element found = findElementWithId((Element) element.getChildNodes().item(i));
                if (found != null) {
                    return found;
                }
            }
        }
        
        return null;
    }
}
