package com.example.demo.client;

import com.example.demo.service.HttpClientService;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Component
public class ReinfClient {

    @Autowired
    private HttpClientService httpClientService;

    // URLs da Receita Federal para REINF
    private static final String URL_PRODUCAO = "https://reinf.receita.fazenda.gov.br/WsReinfRecepcaoLoteEventos/RecepcaoLoteEventos.svc";
    private static final String URL_HOMOLOGACAO = "https://reinf-h.receita.fazenda.gov.br/WsReinfRecepcaoLoteEventos/RecepcaoLoteEventos.svc";

    public String sendEvent(String signedXml, String ambiente) throws Exception {
        String url = "1".equals(ambiente) ? URL_PRODUCAO : URL_HOMOLOGACAO;
        
        // Criar envelope SOAP
        String soapEnvelope = createSoapEnvelope(signedXml);
        
        try (CloseableHttpClient httpClient = httpClientService.createHttpClientForEmpresa("")) {
            HttpPost httpPost = new HttpPost(url);
            
            // Configurar body
            StringEntity entity = new StringEntity(soapEnvelope, StandardCharsets.UTF_8);
            httpPost.setEntity(entity);

            // Configurar headers
            httpPost.setHeader("Content-Type", "text/xml; charset=utf-8");
            httpPost.setHeader("SOAPAction", "\"http://sped.fazenda.gov.br/WsReinfRecepcaoLoteEventos/IRecepcaoLoteEventos/ReceberLoteEventos\"");
            httpPost.setHeader("Content-Length", String.valueOf(entity.getContentLength()));
            
            // Executar requisição
            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                HttpEntity responseEntity = response.getEntity();
                String responseBody = EntityUtils.toString(responseEntity, StandardCharsets.UTF_8);
                
                if (response.getStatusLine().getStatusCode() != 200) {
                    throw new Exception("Erro na comunicação com a Receita Federal. Status: " + 
                                      response.getStatusLine().getStatusCode() + 
                                      ". Response: " + responseBody);
                }
                
                return responseBody;
            }
        }
    }

    private String createSoapEnvelope(String signedXml) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
               "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
               "xmlns:sped=\"http://sped.fazenda.gov.br/WsReinfRecepcaoLoteEventos\">" +
               "<soap:Header/>" +
               "<soap:Body>" +
               "<sped:ReceberLoteEventos>" +
               "<sped:loteEventos>" +
               signedXml +
               "</sped:loteEventos>" +
               "</sped:ReceberLoteEventos>" +
               "</soap:Body>" +
               "</soap:Envelope>";
    }

    public String extractProtocolFromResponse(String soapResponse) {
        try {
            // Extrair número do protocolo da resposta SOAP
            // Esta é uma implementação simplificada - em produção seria melhor usar um parser XML
            if (soapResponse.contains("<numeroProtocolo>")) {
                int start = soapResponse.indexOf("<numeroProtocolo>") + "<numeroProtocolo>".length();
                int end = soapResponse.indexOf("</numeroProtocolo>");
                return soapResponse.substring(start, end);
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public boolean isSuccessResponse(String soapResponse) {
        // Verificar se a resposta indica sucesso
        return soapResponse.contains("<status>0</status>") || 
               soapResponse.contains("sucesso") ||
               soapResponse.contains("numeroProtocolo");
    }
}
