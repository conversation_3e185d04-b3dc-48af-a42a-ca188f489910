package com.example.demo.service;

import com.example.demo.client.ReinfClient;
import com.example.demo.dto.ReinfEventRequest;
import com.example.demo.dto.ReinfEventResponse;
import com.example.demo.util.DigitalSignatureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReinfService {

    private final DigitalSignatureUtil digitalSignatureUtil;
    private final ReinfClient reinfClient;

    public ReinfEventResponse sendEvent(ReinfEventRequest request) {
        try {
            log.info("Iniciando envio de evento REINF");
            
            // Validar request
            validateRequest(request);
            
            // Assinar XML
            log.info("Assinando XML digitalmente");
            String signedXml = digitalSignatureUtil.signXml(
                request.getXmlContent(),
                request.getCertificatePath(),
                request.getCertificatePassword()
            );
            
            log.info("XML assinado com sucesso");
            
            // Enviar para Receita Federal
            log.info("Enviando evento para Receita Federal");
            String response = reinfClient.sendEvent(signedXml, request.getAmbiente());
            
            // Processar resposta
            if (reinfClient.isSuccessResponse(response)) {
                String protocolNumber = reinfClient.extractProtocolFromResponse(response);
                log.info("Evento enviado com sucesso. Protocolo: {}", protocolNumber);
                return ReinfEventResponse.success(protocolNumber, response);
            } else {
                log.error("Erro no envio do evento: {}", response);
                return ReinfEventResponse.error("Erro no envio do evento", response);
            }
            
        } catch (Exception e) {
            log.error("Erro ao processar evento REINF", e);
            return ReinfEventResponse.error("Erro interno: " + e.getMessage(), e.toString());
        }
    }

    private void validateRequest(ReinfEventRequest request) throws Exception {
        if (request.getXmlContent() == null || request.getXmlContent().trim().isEmpty()) {
            throw new Exception("Conteúdo XML é obrigatório");
        }
        
        if (request.getCertificatePath() == null || request.getCertificatePath().trim().isEmpty()) {
            throw new Exception("Caminho do certificado é obrigatório");
        }
        
        if (request.getCertificatePassword() == null || request.getCertificatePassword().trim().isEmpty()) {
            throw new Exception("Senha do certificado é obrigatória");
        }
        
        if (request.getAmbiente() == null || 
            (!request.getAmbiente().equals("1") && !request.getAmbiente().equals("2"))) {
            throw new Exception("Ambiente deve ser '1' (Produção) ou '2' (Homologação)");
        }
    }
}
