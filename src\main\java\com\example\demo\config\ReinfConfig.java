package com.example.demo.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix = "reinf")
@Data
public class ReinfConfig {
    
    private Certificate certificate = new Certificate();
    private String defaultEnvironment = "2";
    
    @Data
    public static class Certificate {
        private Default defaultCert = new Default();
        
        @Data
        public static class Default {
            private String path = "C:/certificados-chave/FEDERAL/FEDERAL_GOIANIA.pfx";
            private String password = "Federallcom13";
        }
    }
}
