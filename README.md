# REINF Integration Service

Este projeto implementa uma integração completa com o sistema REINF da Receita Federal, incluindo assinatura digital e envio de eventos.

## Funcionalidades

- ✅ Assinatura digital de eventos XML usando certificado A1 (PKCS#12)
- ✅ Envio de eventos para a Receita Federal via API REST
- ✅ Suporte para ambientes de produção e homologação
- ✅ Validação de XML e certificados
- ✅ Logs detalhados para auditoria
- ✅ Endpoints REST para integração

## Configuração

### Certificado Digital

O certificado deve estar no formato PKCS#12 (.pfx) e configurado em:
- **<PERSON><PERSON><PERSON> padr<PERSON>**: `C:/certificados-chave/FEDERAL/FEDERAL_GOIANIA.pfx`
- **Senha padrão**: `Federallcom13`

### Ambientes

- **Produção**: `ambiente = "1"`
- **Homologação**: `ambiente = "2"` (padrão)

## Endpoints

### 1. Health Check
```
GET /api/reinf/health
```

### 2. Envio <PERSON> Evento (Completo)
```
POST /api/reinf/send-event
Content-Type: application/json

{
  "xmlContent": "<Reinf>...</Reinf>",
  "certificatePath": "C:/certificados-chave/FEDERAL/FEDERAL_GOIANIA.pfx",
  "certificatePassword": "Federallcom13",
  "ambiente": "2"
}
```

### 3. Envio de Evento (Certificado Padrão)
```
POST /api/reinf/send-event-with-default-cert
Content-Type: application/json

<Reinf xmlns="http://www.reinf.esocial.gov.br/schemas/evtPrestadorServicos/v2_01_02">
    <evtServPrest id="ID0091480300015142025050912313100013">
        ...
    </evtServPrest>
</Reinf>
```

## Exemplo de Uso

### Usando curl:

```bash
# Health check
curl -X GET http://localhost:8080/api/reinf/health

# Envio com certificado padrão
curl -X POST http://localhost:8080/api/reinf/send-event-with-default-cert \
  -H "Content-Type: application/json" \
  -d @src/main/resources/example-event.xml
```

## Resposta de Sucesso

```json
{
  "success": true,
  "message": "Evento enviado com sucesso",
  "protocolNumber": "123456789",
  "receiptXml": "<soap:Envelope>...</soap:Envelope>",
  "errorDetails": null
}
```

## Resposta de Erro

```json
{
  "success": false,
  "message": "Erro no envio do evento",
  "protocolNumber": null,
  "receiptXml": null,
  "errorDetails": "Detalhes do erro..."
}
```

## Como Executar

1. **Instalar dependências**:
   ```bash
   ./mvnw clean install
   ```

2. **Executar aplicação**:
   ```bash
   ./mvnw spring-boot:run
   ```

3. **Executar testes**:
   ```bash
   ./mvnw test
   ```

## Tecnologias Utilizadas

- **Spring Boot 3.5.3**
- **Java 21**
- **Apache XML Security** (para assinatura digital)
- **Bouncy Castle** (para manipulação de certificados)
- **Apache HttpClient** (para comunicação HTTP)
- **Lombok** (para redução de boilerplate)

## Estrutura do Projeto

```
src/main/java/com/example/demo/
├── config/          # Configurações
├── controller/      # Controllers REST
├── dto/            # Data Transfer Objects
├── service/        # Lógica de negócio
├── client/         # Clientes para APIs externas
└── util/           # Utilitários (assinatura digital)
```

## Logs

A aplicação gera logs detalhados para auditoria:
- Início e fim de processamento
- Assinatura digital
- Comunicação com Receita Federal
- Erros e exceções

## Segurança

- Certificados são carregados de forma segura
- Senhas não são expostas nos logs
- Comunicação HTTPS com a Receita Federal
- Validação de entrada rigorosa
