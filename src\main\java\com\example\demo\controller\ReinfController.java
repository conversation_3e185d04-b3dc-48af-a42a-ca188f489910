package com.example.demo.controller;

import com.example.demo.config.ReinfConfig;
import com.example.demo.dto.ReinfEventRequest;
import com.example.demo.dto.ReinfEventResponse;
import com.example.demo.service.ReinfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/reinf")
@RequiredArgsConstructor
@Slf4j
public class ReinfController {

    private final ReinfService reinfService;
    private final ReinfConfig reinfConfig;

    @PostMapping("/send-event")
    public ResponseEntity<ReinfEventResponse> sendEvent(@RequestBody ReinfEventRequest request) {
        log.info("Recebida requisição para envio de evento REINF");
        
        ReinfEventResponse response = reinfService.sendEvent(request);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/send-event-with-default-cert")
    public ResponseEntity<ReinfEventResponse> sendEventWithDefaultCert(@RequestBody String xmlContent) {
        log.info("Recebida requisição para envio de evento REINF com certificado padrão");

        ReinfEventRequest request = new ReinfEventRequest();
        request.setXmlContent(xmlContent);
        request.setCertificatePath(reinfConfig.getCertificate().getDefaultCert().getPath());
        request.setCertificatePassword(reinfConfig.getCertificate().getDefaultCert().getPassword());
        request.setAmbiente(reinfConfig.getDefaultEnvironment());

        ReinfEventResponse response = reinfService.sendEvent(request);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("REINF Service is running");
    }

    @PostMapping("/test-xml-validation")
    public ResponseEntity<String> testXmlValidation(@RequestBody String xmlContent) {
        log.info("Recebido XML para validação: {}", xmlContent.substring(0, Math.min(100, xmlContent.length())));

        try {
            // Apenas validar se o XML é bem formado
            javax.xml.parsers.DocumentBuilderFactory dbf = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder db = dbf.newDocumentBuilder();
            org.w3c.dom.Document doc = db.parse(new java.io.ByteArrayInputStream(xmlContent.getBytes("UTF-8")));

            // Verificar se tem o elemento com ID
            org.w3c.dom.Element root = doc.getDocumentElement();
            org.w3c.dom.NodeList elements = root.getElementsByTagName("*");
            String foundId = null;
            for (int i = 0; i < elements.getLength(); i++) {
                org.w3c.dom.Element element = (org.w3c.dom.Element) elements.item(i);
                if (element.hasAttribute("id")) {
                    foundId = element.getAttribute("id");
                    break;
                }
            }

            return ResponseEntity.ok("XML válido! ID encontrado: " + foundId);
        } catch (Exception e) {
            log.error("Erro ao validar XML", e);
            return ResponseEntity.badRequest().body("Erro ao validar XML: " + e.getMessage());
        }
    }
}
