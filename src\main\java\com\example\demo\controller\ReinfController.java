package com.example.demo.controller;

import com.example.demo.config.ReinfConfig;
import com.example.demo.dto.ReinfEventRequest;
import com.example.demo.dto.ReinfEventResponse;
import com.example.demo.service.ReinfService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/reinf")
@RequiredArgsConstructor
@Slf4j
public class ReinfController {

    private final ReinfService reinfService;
    private final ReinfConfig reinfConfig;

    @PostMapping("/send-event")
    public ResponseEntity<ReinfEventResponse> sendEvent(@RequestBody ReinfEventRequest request) {
        log.info("Recebida requisição para envio de evento REINF");
        
        ReinfEventResponse response = reinfService.sendEvent(request);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/send-event-with-default-cert")
    public ResponseEntity<ReinfEventResponse> sendEventWithDefaultCert(@RequestBody String xmlContent) {
        log.info("Recebida requisição para envio de evento REINF com certificado padrão");

        ReinfEventRequest request = new ReinfEventRequest();
        request.setXmlContent(xmlContent);
        request.setCertificatePath(reinfConfig.getCertificate().getDefaultCert().getPath());
        request.setCertificatePassword(reinfConfig.getCertificate().getDefaultCert().getPassword());
        request.setAmbiente(reinfConfig.getDefaultEnvironment());

        ReinfEventResponse response = reinfService.sendEvent(request);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("REINF Service is running");
    }
}
