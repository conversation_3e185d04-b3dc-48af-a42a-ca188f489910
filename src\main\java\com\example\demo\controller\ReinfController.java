package com.example.demo.controller;

import com.example.demo.config.ReinfConfig;
import com.example.demo.dto.ReinfEventRequest;
import com.example.demo.dto.ReinfEventResponse;
import com.example.demo.service.ReinfService;
import com.example.demo.util.DigitalSignatureUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/reinf")
@RequiredArgsConstructor
@Slf4j
public class ReinfController {

    private final ReinfService reinfService;
    private final ReinfConfig reinfConfig;
    private final DigitalSignatureUtil digitalSignatureUtil;

    @PostMapping("/send-event")
    public ResponseEntity<ReinfEventResponse> sendEvent(@RequestBody ReinfEventRequest request) {
        log.info("Recebida requisição para envio de evento REINF");
        
        ReinfEventResponse response = reinfService.sendEvent(request);
        
        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @PostMapping("/send-event-with-default-cert")
    public ResponseEntity<ReinfEventResponse> sendEventWithDefaultCert(@RequestBody String xmlContent) {
        log.info("Recebida requisição para envio de evento REINF com certificado padrão");

        ReinfEventRequest request = new ReinfEventRequest();
        request.setXmlContent(xmlContent);
        request.setCertificatePath(reinfConfig.getCertificate().getDefaultCert().getPath());
        request.setCertificatePassword(reinfConfig.getCertificate().getDefaultCert().getPassword());
        request.setAmbiente(reinfConfig.getDefaultEnvironment());

        ReinfEventResponse response = reinfService.sendEvent(request);

        if (response.isSuccess()) {
            return ResponseEntity.ok(response);
        } else {
            return ResponseEntity.badRequest().body(response);
        }
    }

    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("REINF Service is running");
    }

    @GetMapping("/check-certificate")
    public ResponseEntity<String> checkCertificate() {
        String certPath = reinfConfig.getCertificate().getDefaultCert().getPath();
        java.io.File certFile = new java.io.File(certPath);

        if (certFile.exists()) {
            return ResponseEntity.ok("Certificado encontrado em: " + certPath);
        } else {
            return ResponseEntity.badRequest().body("Certificado NÃO encontrado em: " + certPath);
        }
    }

    @PostMapping("/debug-soap")
    public ResponseEntity<String> debugSoap(@RequestBody ReinfEventRequest request) {
        try {
            log.info("Gerando SOAP envelope para debug");

            // Assinar XML
            String signedXml = digitalSignatureUtil.signXml(
                request.getXmlContent(),
                request.getCertificatePath(),
                request.getCertificatePassword()
            );

            // Criar envelope SOAP (usando método do ReinfClient)
            String soapEnvelope = createDebugSoapEnvelope(signedXml);

            return ResponseEntity.ok("SOAP Envelope gerado:\n\n" + soapEnvelope);

        } catch (Exception e) {
            log.error("Erro ao gerar SOAP envelope", e);
            return ResponseEntity.badRequest().body("Erro: " + e.getMessage());
        }
    }

    private String createDebugSoapEnvelope(String signedXml) {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
               "<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\" " +
               "xmlns:sped=\"http://sped.fazenda.gov.br/WsReinfRecepcaoLoteEventos\">" +
               "<soap:Header/>" +
               "<soap:Body>" +
               "<sped:ReceberLoteEventos>" +
               "<sped:loteEventos>" +
               signedXml +
               "</sped:loteEventos>" +
               "</sped:ReceberLoteEventos>" +
               "</soap:Body>" +
               "</soap:Envelope>";
    }

    @PostMapping("/test-xml-validation")
    public ResponseEntity<String> testXmlValidation(@RequestBody String xmlContent) {
        log.info("Recebido XML para validação: {}", xmlContent.substring(0, Math.min(100, xmlContent.length())));

        try {
            // Apenas validar se o XML é bem formado
            javax.xml.parsers.DocumentBuilderFactory dbf = javax.xml.parsers.DocumentBuilderFactory.newInstance();
            dbf.setNamespaceAware(true);
            javax.xml.parsers.DocumentBuilder db = dbf.newDocumentBuilder();
            org.w3c.dom.Document doc = db.parse(new java.io.ByteArrayInputStream(xmlContent.getBytes("UTF-8")));

            // Verificar se tem o elemento com ID
            org.w3c.dom.Element root = doc.getDocumentElement();
            org.w3c.dom.NodeList elements = root.getElementsByTagName("*");
            String foundId = null;
            for (int i = 0; i < elements.getLength(); i++) {
                org.w3c.dom.Element element = (org.w3c.dom.Element) elements.item(i);
                if (element.hasAttribute("id")) {
                    foundId = element.getAttribute("id");
                    break;
                }
            }

            return ResponseEntity.ok("XML válido! ID encontrado: " + foundId);
        } catch (Exception e) {
            log.error("Erro ao validar XML", e);
            return ResponseEntity.badRequest().body("Erro ao validar XML: " + e.getMessage());
        }
    }
}
