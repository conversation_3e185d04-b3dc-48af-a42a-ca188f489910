package com.example.demo.controller;

import com.example.demo.config.ReinfConfig;
import com.example.demo.service.ReinfService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.content;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@WebMvcTest(ReinfController.class)
class ReinfControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ReinfService reinfService;

    @MockBean
    private ReinfConfig reinfConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void healthCheck_ShouldReturnOk() throws Exception {
        mockMvc.perform(get("/api/reinf/health"))
                .andExpect(status().isOk())
                .andExpect(content().string("REINF Service is running"));
    }
}
